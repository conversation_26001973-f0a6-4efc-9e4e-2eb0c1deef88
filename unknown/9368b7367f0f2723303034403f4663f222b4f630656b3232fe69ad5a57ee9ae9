# Open SWE Documentation

The documentation site for Open SWE, built with Mintlify to provide comprehensive guides and API references.

## Documentation

For the complete documentation, visit [docs.langchain.com/labs/swe/](https://docs.langchain.com/labs/swe/).

## Development

1. Install dependencies: `yarn install` (from repository root)
2. Start the development server: `yarn dev` (from apps/docs directory)

This will start a local development server at http://localhost:3000 where you can preview the documentation.
