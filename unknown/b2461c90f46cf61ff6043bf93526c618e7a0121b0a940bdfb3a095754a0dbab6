---
title: "From the UI"
description: "How to use Open SWE from the UI"
---

Open SWE provides a powerful web interface that allows you to interact with the coding agent through a chat-like experience. The UI supports both automated and manual workflows, giving you control over how the agent processes your requests.

## Auto vs Manual Mode

The UI offers two distinct modes for handling your coding requests:

<Note>
  You can toggle between auto and manual mode using the checklist icon in the
  main input area.
</Note>

### Auto Mode

When **Auto Mode** is enabled (checklist icon is highlighted):

- Plans are automatically accepted and executed without user intervention
- The agent proceeds directly from planning to implementation
- Ideal for straightforward requests where you trust the agent's planning

### Manual Mode

When **Manual Mode** is active (checklist icon is not highlighted):

- You must manually review and accept proposed plans before execution
- Provides opportunity to edit, modify, or reject plans
- Allows for more control over the implementation approach

<Tip>
  Start with manual mode for important changes to review the agent's approach
  before execution.
</Tip>

## Manager Agent Capabilities

The Manager agent acts as the central orchestrator, intelligently routing your messages and managing the overall workflow. Here's what the Manager can and cannot do:

### What the manager _can_ do

<Steps>
  <Step title="Respond to User Messages">
    Provides contextual responses and status updates about ongoing operations.
    You can ask the manager what it can do, what the status of different agents
    is, etc.
  </Step>
  <Step title="Create New Planning Runs">
    Initiates new planning sessions when you submit coding requests. If your
    message contains a coding request, the manager will create a new planning
    session & corresponding GitHub issue.
  </Step>
  <Step title="Send Messages to Active Planning Runs">
    Forwards additional context or requirements to running planner sessions. If
    you send a message while the planner is running, the manager can forward
    that message to the planner, without interrupting its flow.
  </Step>
  <Step title="Resume Interrupted Planners">
    Resumes planner sessions that were paused for the user to accept or reject
    plans. If the planner has paused for plan acceptance, you can message the
    manager with feedback about the plan. This will be forwarded to the planner,
    and it will continue its flow based on your feedback.
  </Step>
  <Step title="Send Messages to Active Programmer Runs">
    Provides additional context or instructions to running implementation
    sessions. If you send a message while the programmer is running, the manager
    can forward that message to the programmer, without interrupting its flow.
  </Step>
  <Step title="Create new tasks">
    Creates new tasks, independent of the current request. If you send a message
    to the manager with a request for a task that is unrelated to the current
    request, or can be implemented in parallel via a different session, the
    manager will create a new GitHub issue, and initiate a new planning session
    for that issue.
  </Step>
</Steps>

### What the Manager _cannot_ Do

<Note>
  The Manager has several important limitations to ensure proper workflow
  control:
</Note>

- **Cannot create new Programmer runs** - This only happens after plan acceptance or in auto mode.
- **Cannot stop running Planner/Programmer sessions** - To stop a session, you must click the cancel button in the UI.
- **Cannot re-plan while Programmer is running** - Once the programmer session has started, you can not go back to the planner. However, you can send a message to the manager which can be forwarded to the programmer.
- **Cannot open Pull Requests directly** - PRs are created automatically after Programmer completion (and changes are automatically committed anytime a file is modified).
- **Cannot accept plans in manual mode** - You must manually click accept for plan approval.

## Message Handling and Routing

The Manager intelligently classifies your messages and routes them to the appropriate component:

### Message Classification

When you send a message, the Manager analyzes:

- Current status of Planner and Programmer graphs
- Content and intent of your message
- Existing conversation context
- Active plans and tasks

### Routing Options

Based on the analysis, messages are routed to:

- **Start Planner**: For new coding requests requiring planning
- **Update Planner**: To add context to active planning sessions
- **Resume Planner**: To continue interrupted planning with new information
- **Update Programmer**: To provide context to active implementation sessions
- **Create New Issue**: For independent requests that should be separate GitHub issues
- **No Operation**: For messages that don't require specific routing

## Planning Runs

Planning runs are handled by the Planner graph, which creates detailed execution plans for your requests.

### Planning Process

<Steps>
  <Step title="Context Gathering">
    The Planner analyzes your repository and gathers relevant context about the
    codebase
  </Step>
  <Step title="Plan Generation">
    Creates a structured plan with specific, actionable steps
  </Step>
  <Step title="Plan Presentation">
    Presents the proposed plan for review (in manual mode) or automatic
    acceptance (in auto mode)
  </Step>
</Steps>

### Plan Interruption

Plans are presented as interruptions that require user response:

- **Manual Mode**: You must explicitly accept or reject the plan
- **Auto Mode**: Plans are automatically accepted and execution begins
- **Plan Editing**: You can modify proposed plans before acceptance
- **Feedback**: You can provide feedback to the manager if you want the plan to be changed in some way

### Automatic Approval

When auto mode is enabled, plans are automatically accepted and implementation begins immediately after plan generation.

## Programmer Runs

Once a plan is accepted, the Programmer graph executes the implementation.

### Programming Process

<Steps>
  <Step title="Plan Execution">
    Works through each step of the accepted plan systematically
  </Step>
  <Step title="Code Implementation">
    Makes actual changes to files in your repository
  </Step>
  <Step title="Progress Tracking">
    Updates plan status and provides summaries of completed work
  </Step>
  <Step title="Pull Request Creation">
    Automatically opens a PR with all changes when implementation is complete
  </Step>
</Steps>

<Note>
  The Programmer automatically commits changes after each step, ensuring your
  work is preserved even if the session is interrupted.
</Note>

## Getting Started

To begin using the Open SWE UI:

<Steps>
  <Step title="Select Repository">
    Choose your GitHub repository and branch using the repository selector
  </Step>
  <Step title="Choose Mode">
    Toggle auto/manual mode based on your preference for plan approval
  </Step>
  <Step title="Submit Request">
    Type your coding request in the terminal input and press Cmd+Enter to send
  </Step>
  <Step title="Monitor Progress">
    Watch as the Manager routes your request and coordinates the planning and
    implementation
  </Step>
</Steps>

<Note>
  Make sure you have properly configured your GitHub App and authentication
  before using the UI. See the [Development Setup](/labs/swe/setup/development)
  guide for details.
</Note>
