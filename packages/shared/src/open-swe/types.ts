import "@langchain/langgraph/zod";
import { z } from "zod";
import {
  LangGraphRunnableConfig,
  Messages,
  messagesStateReducer,
  MessagesZodState,
} from "@langchain/langgraph/web";
import { MODEL_OPTIONS, MODEL_OPTIONS_NO_THINKING } from "./models.js";
import { ConfigurableFieldUIMetadata } from "../configurable-metadata.js";
import {
  uiMessageReducer,
  type UIMessage,
  type RemoveUIMessage,
} from "@langchain/langgraph-sdk/react-ui";
import {
  GITHUB_INSTALLATION_NAME,
  GITHUB_INSTALLATION_TOKEN_COOKIE,
  GITHUB_TOKEN_COOKIE,
  GITHUB_USER_ID_HEADER,
  GITHUB_USER_LOGIN_HEADER,
  GITHUB_PAT,
  DEFAULT_MCP_SERVERS,
  GITHUB_INSTALLATION_ID,
} from "../constants.js";
import { withLangGraph } from "@langchain/langgraph/zod";
import { BaseMessage } from "@langchain/core/messages";
import { tokenDataReducer } from "../caching.js";

export interface CacheMetrics {
  cacheCreationInputTokens: number;
  cacheReadInputTokens: number;
  inputTokens: number;
  outputTokens: number;
}

export interface ModelTokenData extends CacheMetrics {
  /**
   * The model name that generated this token usage data
   * e.g., "anthropic:claude-sonnet-4-0", "openai:gpt-4.1-mini"
   */
  model: string;
}

export type PlanItem = {
  /**
   * The index of the plan item. This is the order in which
   * it should be executed.
   */
  index: number;
  /**
   * The actual task to perform.
   */
  plan: string;
  /**
   * Whether or not the plan item has been completed.
   */
  completed: boolean;
  /**
   * A summary of the completed task.
   */
  summary?: string;
};

export type PlanRevision = {
  /**
   * The revision index of the plan.
   * This is used to track edits made to the plan by the agent or user
   */
  revisionIndex: number;
  /**
   * The plans for this task & revision.
   */
  plans: PlanItem[];
  /**
   * Timestamp when this revision was created
   */
  createdAt: number;
  /**
   * Who created this revision (agent or user)
   */
  createdBy: "agent" | "user";
};

export type Task = {
  /**
   * Unique identifier for the task
   */
  id: string;
  /**
   * The index of the user's task in chronological order
   */
  taskIndex: number;
  /**
   * The original user request that created this task
   */
  request: string;
  /**
   * The title of the task. Generated by the LLM.
   */
  title: string;
  /**
   * When the task was created
   */
  createdAt: number;
  /**
   * Whether the task is completed
   */
  completed: boolean;
  /**
   * When the task was completed (if applicable)
   */
  completedAt?: number;
  /**
   * Overall summary of the completed task
   */
  summary?: string;
  /**
   * The plans generated for this task.
   * Ordered by revisionIndex, with the latest revision being the active one
   */
  planRevisions: PlanRevision[];
  /**
   * Index of the currently active plan revision
   */
  activeRevisionIndex: number;
  /**
   * Optional parent task id if this task was derived from another task
   */
  parentTaskId?: string;
  /**
   * The pull request number associated with this task
   */
  pullRequestNumber?: number;
};

export type TaskPlan = {
  /**
   * All tasks in the system
   */
  tasks: Task[];
  /**
   * Index of the currently active task
   */
  activeTaskIndex: number;
};

export type TargetRepository = {
  owner: string;
  repo: string;
  branch?: string;
  baseCommit?: string;
};

export type CustomRules = {
  generalRules?: string;
  repositoryStructure?: string;
  dependenciesAndInstallation?: string;
  testingInstructions?: string;
  pullRequestFormatting?: string;
};

export const GraphAnnotation = MessagesZodState.extend({
  /**
   * The internal messages. These are the messages which are
   * passed to the LLM, truncated, removed etc. The main `messages`
   * key is never modified to persist the content show on the client.
   */
  internalMessages: withLangGraph(z.custom<BaseMessage[]>(), {
    reducer: {
      schema: z.custom<Messages>(),
      fn: messagesStateReducer,
    },
    jsonSchemaExtra: {
      langgraph_type: "messages",
    },
    default: () => [],
  }),
  /**
   * The task plan generated by the planning agent, including any
   * user modifications/agent modifications.
   */
  taskPlan: withLangGraph(z.custom<TaskPlan>(), {
    reducer: {
      schema: z.custom<TaskPlan>(),
      fn: (_state, update) => update,
    },
  }),
  /**
   * Notes taken based on the actions performed by the planning agent.
   */
  contextGatheringNotes: withLangGraph(z.custom<string>(), {
    reducer: {
      schema: z.custom<string>(),
      fn: (_state, update) => update,
    },
    default: () => "",
  }),
  /**
   * The session ID of the Sandbox to use.
   */
  sandboxSessionId: withLangGraph(z.custom<string>(), {
    reducer: {
      schema: z.custom<string>(),
      fn: (_state, update) => update,
    },
    default: () => "",
  }),
  /**
   * The name of the branch changes in this thread will be pushed to
   */
  branchName: withLangGraph(z.custom<string>(), {
    reducer: {
      schema: z.custom<string>(),
      fn: (_state, update) => update,
    },
    default: () => "",
  }),
  /**
   * The target repository information
   */
  targetRepository: withLangGraph(z.custom<TargetRepository>(), {
    reducer: {
      schema: z.custom<TargetRepository>(),
      fn: (_state, update) => update,
    },
  }),
  /**
   * The current tree of the codebase the agent is working with.
   */
  codebaseTree: withLangGraph(z.custom<string>(), {
    reducer: {
      schema: z.custom<string>(),
      fn: (_state, update) => update,
    },
  }),
  /**
   * Cache of fetched document content keyed by URLs.
   */
  documentCache: withLangGraph(z.custom<Record<string, string>>(), {
    reducer: {
      schema: z.custom<Record<string, string>>(),
      fn: (state, update) => ({ ...state, ...update }),
    },
    default: () => ({}),
  }),
  /**
   * The ID of the Github issue this thread is associated with
   */
  githubIssueId: withLangGraph(z.custom<number>(), {
    reducer: {
      schema: z.custom<number>(),
      fn: (_state, update) => update,
    },
  }),
  /**
   * Whether or not the dependencies have been installed already in the sandbox.
   */
  dependenciesInstalled: withLangGraph(z.custom<boolean>(), {
    reducer: {
      schema: z.custom<boolean>(),
      fn: (_state, update) => update,
    },
    default: () => false,
  }),
  /**
   * User defined rules.
   */
  customRules: withLangGraph(z.custom<CustomRules>().optional(), {
    reducer: {
      schema: z.custom<CustomRules>().optional(),
      fn: (_state, update) => update,
    },
  }),
  /**
   * The number of times the reviewer subgraph has been executed.
   */
  reviewsCount: withLangGraph(z.custom<number>(), {
    reducer: {
      schema: z.custom<number>(),
      fn: (_state, update) => update,
    },
    default: () => 0,
  }),

  tokenData: withLangGraph(z.custom<ModelTokenData[]>().optional(), {
    reducer: {
      schema: z.custom<ModelTokenData[]>().optional(),
      fn: tokenDataReducer,
    },
  }),

  // ---NOT USED---
  ui: z
    .custom<UIMessage[]>()
    .default(() => [])
    .langgraph.reducer<(UIMessage | RemoveUIMessage)[]>((state, update) =>
      uiMessageReducer(state, update),
    ),
  // TODO: Not used, but can be used in the future for Gen UI artifacts
  context: z.record(z.string(), z.unknown()),
});

export type GraphState = z.infer<typeof GraphAnnotation>;
export type GraphUpdate = Partial<GraphState>;

export const GraphConfigurationMetadata: {
  [key: string]: {
    x_open_swe_ui_config:
      | Omit<ConfigurableFieldUIMetadata, "label">
      | { type: "hidden" };
  };
} = {
  maxContextActions: {
    x_open_swe_ui_config: {
      type: "number",
      default: 75,
      min: 1,
      max: 250,
      description:
        "Maximum number of actions allowed during the context gathering phase. An action consists of a tool call.",
    },
  },
  maxReviewActions: {
    x_open_swe_ui_config: {
      type: "number",
      default: 30,
      min: 1,
      max: 250,
      description:
        "Maximum number of review actions allowed during the review phase. An action consists of a tool call.",
    },
  },
  maxReviewCount: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  plannerModelName: {
    x_open_swe_ui_config: {
      type: "select",
      default: "anthropic:claude-sonnet-4-0",
      description:
        "The model to use for planning tasks. This model should be very good at generating code, and have strong context understanding and reasoning capabilities. It will be used for the most complex tasks throughout the agent.",
      options: MODEL_OPTIONS_NO_THINKING,
    },
  },
  plannerTemperature: {
    x_open_swe_ui_config: {
      type: "slider",
      default: 0,
      min: 0,
      max: 2,
      step: 0.1,
      description: "Controls randomness (0 = deterministic, 2 = creative)",
    },
  },
  programmerModelName: {
    x_open_swe_ui_config: {
      type: "select",
      default: "anthropic:claude-sonnet-4-0",
      description:
        "The model to use for programming/other advanced technical tasks. This model should be very good at generating code, and have strong context understanding and reasoning capabilities. It will be used for the most complex tasks throughout the agent.",
      options: MODEL_OPTIONS_NO_THINKING,
    },
  },
  programmerTemperature: {
    x_open_swe_ui_config: {
      type: "slider",
      default: 0,
      min: 0,
      max: 2,
      step: 0.1,
      description: "Controls randomness (0 = deterministic, 2 = creative)",
    },
  },
  reviewerModelName: {
    x_open_swe_ui_config: {
      type: "select",
      default: "anthropic:claude-sonnet-4-0",
      description:
        "The model to use for reviewer tasks. This model should be very good at generating code, and have strong context understanding and reasoning capabilities. It will be used for the most complex tasks throughout the agent.",
      options: MODEL_OPTIONS_NO_THINKING,
    },
  },
  reviewerTemperature: {
    x_open_swe_ui_config: {
      type: "slider",
      default: 0,
      min: 0,
      max: 2,
      step: 0.1,
      description: "Controls randomness (0 = deterministic, 2 = creative)",
    },
  },
  routerModelName: {
    x_open_swe_ui_config: {
      type: "select",
      default: "anthropic:claude-3-5-haiku-latest",
      description:
        "The model to use for routing tasks, and other simple generations. This model should be good at tool calling/structured output.",
      options: MODEL_OPTIONS,
    },
  },
  routerTemperature: {
    x_open_swe_ui_config: {
      type: "slider",
      default: 0,
      min: 0,
      max: 2,
      step: 0.1,
      description: "Controls randomness (0 = deterministic, 2 = creative)",
    },
  },
  summarizerModelName: {
    x_open_swe_ui_config: {
      type: "select",
      default: "anthropic:claude-sonnet-4-0",
      description:
        "The model to use for summarizing the conversation history, or extracting key context from large inputs. This model should have strong context retention/understanding capabilities, and should be good at tool calling/structured output.",
      options: MODEL_OPTIONS_NO_THINKING,
    },
  },
  summarizerTemperature: {
    x_open_swe_ui_config: {
      type: "slider",
      default: 0,
      min: 0,
      max: 2,
      step: 0.1,
      description: "Controls randomness (0 = deterministic, 2 = creative)",
    },
  },
  maxTokens: {
    x_open_swe_ui_config: {
      type: "number",
      default: 10_000,
      min: 1,
      max: 64_000,
      description:
        "The maximum number of tokens to generate in an individual LLM call. Increasing/decreasing this number _will_ effect how many tokens are generated by the model. It will _not_ simply cut off the generation after the specified number of tokens is reached.",
    },
  },
  mcpServers: {
    x_open_swe_ui_config: {
      type: "json",
      default: JSON.stringify(DEFAULT_MCP_SERVERS, null, 2),
      description:
        "JSON configuration for custom MCP servers. LangGraph docs server is set by default. See the `mcpServers` field of the LangChain MCP Adapters `ClientConfig` type for information on this schema. [Documentation here](https://v03.api.js.langchain.com/types/_langchain_mcp_adapters.ClientConfig.html).",
    },
  },
  apiKeys: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_TOKEN_COOKIE]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_INSTALLATION_TOKEN_COOKIE]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_USER_ID_HEADER]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_USER_LOGIN_HEADER]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_INSTALLATION_NAME]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_INSTALLATION_ID]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  [GITHUB_PAT]: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  thread_id: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
  run_id: {
    x_open_swe_ui_config: {
      type: "hidden",
    },
  },
};

export const GraphConfiguration = z.object({
  /**
   * The thread ID, generated by the manager graph.
   */
  thread_id: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.thread_id,
  }),
  /**
   * The run ID, generated by the manager graph.
   */
  run_id: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.run_id,
  }),
  /**
   * The maximum number of context gathering actions to take during planning.
   * Each action consists of 2 messages (request & result), plus 1 human message.
   * Total messages = maxContextActions * 2 + 1
   * @default 75
   */
  maxContextActions: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.maxContextActions,
  }),
  /**
   * The maximum number of context gathering actions to take during review.
   * Each action consists of 2 messages (request & result), plus 1 human message.
   * Total messages = maxReviewActions * 2 + 1
   * @default 30
   */
  maxReviewActions: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.maxReviewActions,
  }),

  /**
   * The model ID to use for programming/other advanced technical tasks.
   * @default "anthropic:claude-sonnet-4-0"
   */
  plannerModelName: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.plannerModelName,
  }),
  /**
   * The temperature to use for programming/other advanced technical tasks.
   * @default 0
   */
  plannerTemperature: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.plannerTemperature,
  }),

  /**
   * The model ID to use for programming/other advanced technical tasks.
   * @default "anthropic:claude-sonnet-4-0"
   */
  programmerModelName: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.programmerModelName,
  }),
  /**
   * The temperature to use for programming/other advanced technical tasks.
   * @default 0
   */
  programmerTemperature: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.programmerTemperature,
  }),

  /**
   * The model ID to use for programming/other advanced technical tasks.
   * @default "anthropic:claude-sonnet-4-0"
   */
  reviewerModelName: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.reviewerModelName,
  }),
  /**
   * The temperature to use for programming/other advanced technical tasks.
   * @default 0
   */
  reviewerTemperature: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.reviewerTemperature,
  }),

  /**
   * The model ID to use for routing tasks.
   * @default "anthropic:claude-3-5-haiku-latest"
   */
  routerModelName: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.routerModelName,
  }),
  /**
   * The temperature to use for routing tasks.
   * @default 0
   */
  routerTemperature: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.routerTemperature,
  }),

  /**
   * The model ID to use for summarizing the conversation history, or extracting key context from large inputs.
   * @default "anthropic:claude-sonnet-4-0"
   */
  summarizerModelName: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.summarizerModelName,
  }),
  /**
   * The temperature to use for summarizing the conversation history, or extracting key context from large inputs.
   * @default 0
   */
  summarizerTemperature: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.actionGeneratorTemperature,
  }),
  /**
   * The maximum number of tokens to generate in an individual generation.
   * @default 10_000
   */
  maxTokens: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.maxTokens,
  }),
  /**
   * User defined API keys to use
   */
  apiKeys: withLangGraph(z.record(z.string(), z.string()).optional(), {
    metadata: GraphConfigurationMetadata.apiKeys,
  }),
  /**
   * The user's GitHub access token. To be used in requests to get information about the user.
   */
  [GITHUB_TOKEN_COOKIE]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_TOKEN_COOKIE],
  }),
  /**
   * The installation token from the GitHub app. This token allows us to take actions
   * on the repos the user has granted us access to, but on behalf of the app, not the user.
   */
  [GITHUB_INSTALLATION_TOKEN_COOKIE]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_INSTALLATION_TOKEN_COOKIE],
  }),
  /**
   * The user's GitHub ID. Required when creating runs triggered by a bot (e.g. GitHub issue)
   */
  [GITHUB_USER_ID_HEADER]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_USER_ID_HEADER],
  }),
  /**
   * The user's GitHub login. Required when creating runs triggered by a bot (e.g. GitHub issue)
   */
  [GITHUB_USER_LOGIN_HEADER]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_USER_LOGIN_HEADER],
  }),
  /**
   * The installation name of the GitHub app. Required when creating runs triggered by a bot (e.g. GitHub issue)
   */
  [GITHUB_INSTALLATION_NAME]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_INSTALLATION_NAME],
  }),
  /**
   * The installation ID of the GitHub app the user is using to create the run.
   */
  [GITHUB_INSTALLATION_ID]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_INSTALLATION_ID],
  }),
  /**
   * GitHub Personal Access Token. Used for simpler authentication in environments like evals
   * where GitHub App installation tokens are not available or needed.
   */
  [GITHUB_PAT]: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata[GITHUB_PAT],
  }),
  /**
   * Custom MCP servers configuration as JSON string. Merges with default servers.
   * @default Default LangGraph docs MCP server
   */
  mcpServers: withLangGraph(z.string().optional(), {
    metadata: GraphConfigurationMetadata.mcpServers,
  }),
  /**
   * The maxium number of times the reviewer subgraph can be executed.
   */
  maxReviewCount: withLangGraph(z.number().optional(), {
    metadata: GraphConfigurationMetadata.maxReviewCount,
  }),
});

export type GraphConfig = LangGraphRunnableConfig<
  z.infer<typeof GraphConfiguration> & {
    thread_id: string;
    assistant_id: string;
  }
>;

export interface AgentSession {
  threadId: string;
  runId: string;
}
