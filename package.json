{"name": "open-swe", "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "turbo:command": "turbo", "format": "turbo format", "clean": "rm -rf .turbo || true && turbo clean", "format:check": "turbo format:check", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "test": "turbo test"}, "devDependencies": {"turbo": "^2.5.0", "typescript": "^5"}, "resolutions": {"@langchain/langgraph-sdk": "^0.0.95", "@langchain/core": "^0.3.65", "langsmith": "^0.3.48", "jsonwebtoken": "^9.0.2"}, "packageManager": "yarn@3.5.1"}