name: Deploy LangGraph

on:
  push:
    branches: ["main"]
    paths:
      - "apps/open-swe/**"
      - "packages/shared/**"
      - "package.json"
  workflow_dispatch: # Allows manual triggering

# If another push happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    name: Deploy to LangGraph Platform
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build

      - name: Execute deployment
        run: |
          cd apps/open-swe
          yarn tsx ./scripts/deploy-langgraph.ts

        env:
          CONTROL_PLANE_HOST: ${{ secrets.CONTROL_PLANE_HOST }}
          LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
          INTEGRATION_ID: ${{ secrets.INTEGRATION_ID }}
          DEPLOYMENT_ID: ${{ secrets.DEPLOYMENT_ID }}
        timeout-minutes: 35

      - name: Deployment status
        if: always()
        run: echo "Deployment workflow completed with status ${{ job.status }}"
