---
title: Custom Rules
description: Configure Open SWE with project-specific rules using `AGENTS.md`
---

# Custom Rules

Custom rules allow you to provide project-specific context and guidelines to Open SWE through a markdown file in your repository root.

## Getting Started

The easiest way to get started is to have Open SWE itself write this file for you! The Open SWE UI provides a "Generate `AGENTS.md`" quick action which will insert a predefined prompt into the input. This prompt has been designed to generate a well-formatted `AGENTS.md` file from the agent.

![Quick Action Generate AGENTS.md](/images/quick_action_generate_agents_md.png)

## What is `AGENTS.md`?

`AGENTS.md` is a markdown file that tells Open SWE about your project's:

- Coding standards and conventions
- Repository structure and architecture
- Dependencies and installation procedures
- Testing frameworks and practices
- Pull request formatting requirements

## Why Use Custom Rules?

Custom rules help Open SWE:

- Follow your project's specific conventions
- Understand your codebase architecture
- Use the correct package managers and tools
- Write tests that match your testing patterns
- Generate properly formatted pull requests

Without custom rules, Open SWE uses generic best practices that may not align with your project.

## Supported Sections

### `<general_rules>`

Project-specific coding standards and conventions:

- Package manager preferences (e.g., "Always use Yarn")
- Code style requirements
- Import/export patterns
- Architectural guidelines
- Common scripts, and how to run them

Example: "Run `yarn test` to run tests", or "Run `yarn build` to build the project"

### `<repository_structure>`

Description of your codebase organization:

- Monorepo vs single package structure
- Key directories and their purposes
- Package relationships and dependencies
- Build system configuration

Include context about where/how to create new files, apps, packages, etc. in this section.

Example: "When creating new packages, place them inside the `packages` directory."

### `<dependencies_and_installation>`

Package management and setup instructions:

- Package manager commands
- Installation procedures
- Key dependencies and their purposes
- Workspace configuration details

This section should include information about the package manager(s) used in the project, and how/where to install dependencies.

Example: "Always install dependencies inside the specific app/package where they're used, never in the root `package.json` unless adding a resolution."

### `<testing_instructions>`

Testing framework and practices:

- Test runner configuration (Jest, Vitest, etc.)
- Test file naming conventions
- How to run different test types
- Testing best practices for your project (e.g. what types of tests to write)

Example: "Always run `yarn test` after making changes."

### `<pull_request_formatting>`

PR creation and formatting guidelines:

- Title and description templates
- Required sections or checklists
- Review process requirements
- Linking conventions

Example: "The pull request body should include a `Testing Steps` section which includes bullet points describing how to test the changes."

## File Names

Open SWE reads custom rules from these files (in order of precedence):

1. `AGENTS.md` (recommended)
2. `AGENT.md`
3. `CLAUDE.md`
4. `CURSOR.md`

<Note>
  Use `AGENTS.md` as the standard filename for consistency across projects.
</Note>

## Missing Sections

If your custom rules file doesn't include XML section tags, the entire file content will be passed to the prompt inside a custom rules section.

## Formatting Example

If your custom rules file does include the proper XML section tags, only the content from inside the known tags will be passed to the system prompt.

Known tags:
- `<general_rules>`
- `<repository_structure>`
- `<dependencies_and_installation>`
- `<testing_instructions>`
- `<pull_request_formatting>`

Each tag _must_ contain a proper closing tag. If a tag is missing a closing tag, the entire file content will be passed to the prompt inside a custom rules section.

Here is an example showing what your `AGENTS.md` file should look like:

```markdown
<general_rules>

- Always use Yarn as the package manager
- Follow strict TypeScript practices
- Use ESLint and Prettier for code quality
  </general_rules>

<repository_structure>
This is a Yarn workspace monorepo with three main packages:

- apps/web: Next.js frontend
- apps/api: Express backend
- packages/shared: Common utilities
  </repository_structure>

<dependencies_and_installation>
Run `yarn install` from the repository root to install all dependencies.
Key dependencies include React 18, TypeScript, and Jest for testing.
</dependencies_and_installation>

<testing_instructions>

- Run `yarn test` for unit tests
- Run `yarn test:e2e` for end-to-end tests
- Test files use `.test.ts` extension
- Use Jest with React Testing Library
  </testing_instructions>

<pull_request_formatting>
PR titles should follow: "feat: description" or "fix: description"
Include a brief description and link any related issues.
</pull_request_formatting>
```
