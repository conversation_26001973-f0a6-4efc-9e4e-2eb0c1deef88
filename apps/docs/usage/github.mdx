---
title: "From Github"
description: "How to use Open SWE from Github"
---

<Warning>
  GitHub webhooks are _not_ available via the demo application. To use GitHub webhooks, you must set up your own instance of Open SWE following the [development setup guide](/labs/swe/setup/development).
</Warning>

# GitHub Webhook Integration

Open SWE integrates seamlessly with GitHub through webhooks, allowing you to trigger automated code changes directly from GitHub issues. This provides a streamlined workflow where you can request code changes by simply adding labels to issues in repositories where Open SWE is installed.

## Triggering Runs with Labels

Open SWE monitors GitHub issues for specific labels that trigger automated runs. When you add one of these labels to an issue, Open SWE will automatically create a new run to process your request.

### Label Types

Open SWE supports three types of labels that control how the agent operates:

**Manual Mode (`open-swe`)**

- Requires manual approval of the generated plan before code execution
- Gives you full control over what changes will be made
- Ideal for complex or sensitive changes where you want to review the approach first

**Auto Mode (`open-swe-auto`)**

- Automatically approves and executes the generated plan
- Provides faster turnaround for straightforward requests
- Best for simple changes or when you trust the agent to proceed autonomously

**Max Mode (`open-swe-max` and `open-swe-max-auto`)**

- Uses Claude Opus 4.1 for both planning and programming tasks
- Provides enhanced performance and reasoning capabilities for complex problems
- `open-swe-max`: Requires manual plan approval with premium model performance
- `open-swe-max-auto`: Combines automatic execution with premium model capabilities
- Ideal for challenging tasks that benefit from the most advanced AI reasoning

<Note>
  In development environments, the labels are `open-swe-dev`,
  `open-swe-auto-dev`, `open-swe-max-dev`, and `open-swe-max-auto-dev`
  respectively. The system automatically uses the appropriate labels based on
  the `NODE_ENV` environment variable.
</Note>

## Automatic Run Creation

When you add a supported label to a GitHub issue, Open SWE's webhook handler automatically:

1. **Validates the request** - Verifies webhook signatures and authentication
2. **Extracts issue context** - Captures the issue title, description, and metadata
3. **Creates a new thread** - Generates a unique thread ID for the conversation
4. **Starts the Manager Graph** - Initiates the agent workflow with the issue content
5. **Configures execution mode** - Sets auto-accept based on the label type used

The entire process happens within seconds of adding the label, providing immediate feedback through issue comments.

## Issue Comments and Run Links

Once a run is created, Open SWE automatically posts a comment on the triggering issue to confirm processing has started. This comment includes:

- **Status confirmation** - "🤖 Open SWE has been triggered for this issue. Processing..."
- **Run link** - Direct URL to view the run in the Open SWE web interface
- **Access restriction notice** - Clarifies that only the issue creator can access the run
- **Development metadata** - Run ID and thread ID for debugging (in a collapsible section)

<Tip>
  The run link allows you to monitor progress in real-time, view the generated
  plan, and interact with the agent if needed. You can switch between manual and
  auto mode even after the run has started.
</Tip>

## User Access Restrictions

Open SWE implements strict access controls to ensure security and privacy:

### Issue Creator Access

- **Only the user who created the issue** can access the generated run URL
- This prevents unauthorized users from viewing or modifying runs triggered by others
- Access is enforced through GitHub authentication and user verification

### Repository Permissions

- Open SWE respects GitHub's repository permissions
- Users must have appropriate access to the repository to trigger runs
- The GitHub App installation determines which repositories can use Open SWE

<Note>
  If you need to share access to a run with team members, you can do so through
  the Open SWE web interface after the run is created, or by having team members
  with repository access create their own issues.
</Note>

## Pull Request Integration

When Open SWE successfully completes code changes, it automatically creates pull requests that are linked back to the original issue:

### Automatic PR Creation

- **Generated after plan execution** - PRs are created once the Programmer Graph completes its work
- **Linked to triggering issue** - PRs reference the original issue in their description
- **Preserves commit history** - All intermediate commits are maintained for transparency

### Issue Resolution

- **Automatic closure** - When the generated PR is merged, GitHub automatically closes the linked issue
- **Clear audit trail** - The connection between issue, run, and PR provides complete traceability
- **Status updates** - Issue comments track the progress from request to completion

<Tip>
  You can review the generated PR before merging, even in auto mode. The
  auto-accept setting only applies to plan approval, not PR merging, giving you
  final control over what code enters your repository.
</Tip>

## Getting Started

To start using Open SWE with webhooks:

1. **Ensure Open SWE is installed** on your repository as a GitHub App
2. **Create a detailed issue** describing the changes you want
3. **Add the appropriate label** (`open-swe` for manual mode or `open-swe-auto` for automatic mode)
4. **Monitor the issue comments** for the run link and status updates
5. **Review and merge the PR** when Open SWE completes the changes

For setup instructions, see the [Development Setup](/labs/swe/setup/development) guide.
