---
title: "Introduction"
description: "How to use Open SWE"
---

# Using Open SWE

Open SWE provides two primary ways to interact with the coding agent, each designed for different workflows and use cases. Whether you prefer direct interaction through a web interface or automated triggers through GitHub webhooks, Open SWE adapts to your development process.

## Usage Methods

<CardGroup cols={2}>
  <Card title="Web Interface" icon="browser" href="/labs/swe/usage/ui">
    Interactive chat interface with manual and auto modes for direct agent
    communication
  </Card>
  <Card title="GitHub Webhooks" icon="webhook" href="/labs/swe/usage/webhook">
    Automated triggers through GitHub issue labels for seamless repository
    integration
  </Card>
</CardGroup>

## Getting Started

### Try the Demo

You can explore Open SWE's capabilities using our hosted demo at [swe.langchain.com](https://swe.langchain.com).

<Note>
  The demo application requires you to provide your own LLM API keys. For
  production use or development, we recommend setting up your own instance
  following our [development setup guide](/labs/swe/setup/development).
</Note>

### Choose Your Workflow

- **Web Interface**: Best for interactive development, experimentation, and when you want direct control over the agent's planning and execution process
- **GitHub Webhooks**: Ideal for automated workflows, issue-driven development, and team collaboration where coding tasks are tracked through GitHub issues

## What's Next?

<Tip>
  Start with the [Web Interface guide](/labs/swe/usage/ui) to understand Open
  SWE's core capabilities, then explore [GitHub
  Webhooks](/labs/swe/usage/webhook) for automated integration into your
  development workflow.
</Tip>

Both usage methods leverage the same underlying LangGraph agent architecture with specialized graphs for planning, programming, and management, ensuring consistent behavior regardless of how you interact with Open SWE.
