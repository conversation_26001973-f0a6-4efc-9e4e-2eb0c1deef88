---
title: "Secrets"
description: "Environment variables & secrets access in dev environments"
---

Open-SWE implements a secure, encrypted way to share user environment variables with the agent and pass them to the development server.

## Environment Variables & API Keys

### How API Keys Are Protected

Your API keys are protected with industry-standard AES-256-GCM encryption both in transit and at rest.

**Storage Process:**
1. **Frontend**: Temporarily stored in browser localStorage (plain text)
2. **Transit**: Encrypted with AES-256-GCM before sending to backend
3. **Backend**: Stored encrypted in LangGraph's database
4. **Runtime**: Decrypted when necessary using server-side encryption keys

<Accordion title="Technical Encryption Details">
  **Encryption Details:**
  - **Algorithm**: [AES-256-GCM](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-38d.pdf) (authenticated encryption)
  - **Key Derivation**: SHA-256 hash of system `SECRETS_ENCRYPTION_KEY`
  - **IV Generation**: Random 12-byte initialization vector per encryption
  - **Authentication**: 16-byte authentication tag prevents tampering
</Accordion>

### System vs User Environment Variables

Open-SWE handles two types of environment variables:

**System Environment Variables:**
- **Purpose**: Environment variables required to run Open-SWE. If you're self-hosting, you can set LLM, infrastructure, and authentication keys
- **Examples**: `DAYTONA_API_KEY`, `SECRETS_ENCRYPTION_KEY`, `GITHUB_APP_PRIVATE_KEY`
- **Access**: Server-side only via `process.env`
- **Security**: Never exposed to users or sandboxes

**User-Defined Environment Variables:**
- **Purpose**: Personal API keys and custom development variables
- **Examples**: `ANTHROPIC_API_KEY`, `MY_DATABASE_URL`, `STRIPE_TEST_KEY`
- **Access**: User-controlled via settings page in UI
- **Security**: AES-256-GCM encrypted + explicit user consent required to pass them to the development server

### When API Keys Are Exposed to Sandbox Environments

Your API keys are **never automatically exposed** to sandbox environments. Exposure requires explicit user consent.

You may wonder, why does the sandbox environment even need API keys? We've designed Open-SWE to be your junior dev. Part of development is testing code in real-time. **Open-SWE has the ability to spin up development servers that can run your LangGraph agents, Next.js apps, Flask servers, and more, all within the sandbox its running in.** If you think this option can be useful, please review what it means to pass your keys to the sandbox.

<Note>
  Consider using separate development API keys with limited permissions instead of your production keys when enabling sandbox access. This reduces the impact of potential exposure while maintaining functionality.
</Note>

**Default Behavior (Secure):**
- API keys are only used for LLM model initialization & setting up Daytona using your own key, if you passed it
- Keys are NOT available as environment variables in sandboxes or accessible to LLMs
- Each key has `allowedInDev: false` by default

**When You Enable "Include in Dev Server":**
- You manually toggle the switch for each API key
- Key becomes available as an environment variable in sandbox
- You maintain control over which keys are exposed

## Sandbox Security & Isolation

### Container Isolation

Each user session runs in a completely isolated Daytona container with multiple security boundaries. These containers can modify the code in your repo on a new branch and don't exchange information across sessions.

<Accordion title="Isolation Details">
  **Container Isolation:**
  - Separate container per user session
  - No shared file systems between containers
  - Container-level resource limits and quotas

  **Process Isolation:**
  - User environment variables vs system environment variables
  - Sandboxed processes cannot access host system or code

  **Temporal Isolation:**
  - Automatic deletion after 15 minutes of inactivity
  - No persistent storage across sessions
  - Fresh environment for each new session

  **Network Isolation:**
  - Containers cannot communicate with each other
</Accordion>

## Data Handling & Access Control

<Warning>
  When enabling "Include in Dev Server" for API keys, you're expanding the attack surface of your credentials. AI agent may inadvertently expose environment variables in generated code or logs. Only use this feature when necessary for development server monitoring.
</Warning>

### Development Environment Exposure Risks

You should only use this feature if you find it useful to monitor development servers. We recommend not using your main keys and having access control for keys that you pass to sandbox environments.

While the Open-SWE UI and agent exchange these using encryption, **LLM may leave environment variables exposed in the code.**

## Security Best Practices

Our team loves using Open-SWE internally and we recommend the following best practices:

- Use the minimum required permissions for each API key
- Regularly rotate your API keys
- Only enable "Include in Dev Server" when necessary for specific tools
- Monitor the "Last Used" timestamps in settings
- Consider using separate development keys instead of production keys for sandbox environments
- Never enable sandbox access for production database credentials or high-privilege API keys
- Review generated code for accidentally exposed environment variables before deploying

---