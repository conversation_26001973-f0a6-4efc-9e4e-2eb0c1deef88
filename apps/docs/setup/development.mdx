---
title: "Development Setup"
description: "How to set up Open SWE for development"
---

This guide will walk you through setting up Open SWE for local development. You'll need to clone the repository, install dependencies, configure environment variables, create a GitHub App, and start the development servers.

<Note>
  This setup is for development purposes. For production deployment, you'll need
  to adjust URLs and create separate GitHub Apps for production use.
</Note>

## Prerequisites

Before starting, ensure you have the following installed:

- Node.js (version 18 or higher)
- Yarn (version 3.5.1 or higher)
- Git

## Setup Steps

<Steps>
  <Step title="Clone the Repository">
    Clone the Open SWE repository to your local machine:

    ```bash
    git clone https://github.com/langchain-ai/open-swe.git
    ```
    ```bash
    cd open-swe
    ```

  </Step>

  <Step title="Install Dependencies">
    Install all dependencies using Yarn from the repository root:

    ```bash
    yarn install
    ```

    This will install dependencies for all packages in the monorepo workspace.

  </Step>

  <Step title="Set Up Environment Files">
    Copy the environment example files and configure them:

    ```bash
    # Copy web app environment file
    cp apps/web/.env.example apps/web/.env
    ```
    ```bash
    # Copy agent environment file
    cp apps/open-swe/.env.example apps/open-swe/.env
    ```

    ### Web App Environment Variables (`apps/web/.env`)

    Fill in the following variables (GitHub App values will be added in the next step):

    ```bash Environment Variables [expandable]
    # API URLs for development
    NEXT_PUBLIC_API_URL="http://localhost:3000/api"
    LANGGRAPH_API_URL="http://localhost:2024"

    # Encryption key for secrets (generate with: openssl rand -hex 32)
    SECRETS_ENCRYPTION_KEY=""

    # GitHub App OAuth settings (will be filled after creating GitHub App)
    NEXT_PUBLIC_GITHUB_APP_CLIENT_ID=""
    GITHUB_APP_CLIENT_SECRET=""
    GITHUB_APP_REDIRECT_URI="http://localhost:3000/api/auth/github/callback"

    # GitHub App details (will be filled after creating GitHub App)
    GITHUB_APP_NAME="open-swe-dev" # this must match the name of your GitHub app, excluding spaces
    GITHUB_APP_ID=""
    GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
    ...add your private key here...
    -----END RSA PRIVATE KEY-----
    "
    ```

    ### Agent Environment Variables (`apps/open-swe/.env`)

    Configure the agent environment variables:

    ```bash Environment Variables [expandable]
    # LangSmith tracing & LangGraph platform
    LANGCHAIN_PROJECT="default"
    LANGCHAIN_API_KEY="lsv2_pt_..."  # Get from LangSmith
    LANGCHAIN_TRACING_V2="true"
    LANGCHAIN_TEST_TRACKING="false"

    # LLM Provider Keys (at least one required)
    ANTHROPIC_API_KEY=""  # Recommended - default provider
    OPENAI_API_KEY=""     # Optional
    GOOGLE_API_KEY=""     # Optional

    # Infrastructure
    DAYTONA_API_KEY=""    # Required. For cloud sandboxes

    # Tools
    FIRECRAWL_API_KEY=""  # For URL content extraction

    # GitHub App settings (same as web app)
    GITHUB_APP_NAME="open-swe-dev" # this must match the name of your GitHub app, excluding spaces
    GITHUB_APP_ID=""
    GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
    ...add your private key here...
    -----END RSA PRIVATE KEY-----
    "
    GITHUB_WEBHOOK_SECRET=""  # Will be generated in next step

    # Server configuration
    PORT="2024"
    OPEN_SWE_APP_URL="http://localhost:3000"
    SECRETS_ENCRYPTION_KEY=""  # Must match web app value

    # CI/CD. See the /labs/swe/setup/ci for more information
    SKIP_CI_UNTIL_LAST_COMMIT="true"
    ```

    <Tip>
    Generate the `SECRETS_ENCRYPTION_KEY` using: `openssl rand -hex 32`. This key must be identical in both environment files.
    </Tip>

  </Step>

  <Step title="Create GitHub App">
    <Note>
    You'll need to create a **GitHub App** (not a GitHub OAuth App). These are different types of applications with different capabilities. Consider creating separate GitHub apps for development and production environments.
    </Note>

    ### Create the GitHub App

    1. Go to [GitHub App creation page](https://github.com/settings/apps/new)
    2. Fill in the basic information:
       - **GitHub App name**: Your preferred name
       - **Description**: Development instance of Open SWE coding agent
       - **Homepage URL**: Your repository URL
       - **Callback URL**: `http://localhost:3000/api/auth/github/callback`

    ### Configure OAuth Settings

    - ✅ **Request user authorization (OAuth) during installation** - Allows users to log in to the web app
    - ✅ **Redirect on update** - Redirects users back to your app after permission updates
    - ❌ **Expire user authorization tokens** - Keep tokens from expiring

    ### Set Up Webhook

    1. ✅ **Enable webhook**
    2. **Webhook URL**: You'll need to use a tool like ngrok to expose your local server:
       ```bash
       # Install ngrok if you haven't already
       # Then expose your local LangGraph server
       ngrok http 2024
       ```
       Use the ngrok URL + `/webhook/github` (e.g., `https://abc123.ngrok.io/webhook/github`)

    3. **Webhook secret**: Generate and save this value:
       ```bash
       openssl rand -hex 32
       ```
       Add this value to `GITHUB_WEBHOOK_SECRET` in `apps/open-swe/.env`

    ### Configure Permissions

    **Repository permissions:**
    - **Contents**: Read & Write
    - **Issues**: Read & Write
    - **Pull requests**: Read & Write
    - **Metadata**: Read only (automatically enabled)

    **Organization permissions:** None

    **Account permissions:** None

    This gif shows how/where to enable the permissions on the GitHub App:
    <iframe
      className="w-full aspect-video rounded-xl"
      src="https://www.youtube.com/embed/FP02bprNtrA"
      title="Permissions Walkthrough"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
    ></iframe>

    ### Subscribe to Events

    - ✅ **Issues** - Required for webhook functionality

    ### Installation Settings

    - **Where can this GitHub App be installed?**:
      - Choose "Any account" for broader testing
      - Or "Only on this account" to limit to your repositories

    ### Complete App Creation

    Click **Create GitHub App** to finish the setup.

    ### Collect App Credentials

    After creating the app, collect the following values and add them to both environment files:

    - **GITHUB_APP_NAME**: The name you chose
    - **GITHUB_APP_ID**: Found in the "About" section (e.g., `********`)
    - **NEXT_PUBLIC_GITHUB_APP_CLIENT_ID**: Found in the "About" section
    - **GITHUB_APP_CLIENT_SECRET**:
      1. Scroll to "Client secrets" section
      2. Click "Generate new client secret"
      3. Copy the generated value
    - **GITHUB_APP_PRIVATE_KEY**:
      1. Scroll to "Private keys" section
      2. Click "Generate a private key"
      3. Download the `.pem` file and copy its contents
      4. Format as a single line with `\\n` for line breaks, or use the multiline format shown in the example
    - **GITHUB_APP_REDIRECT_URI**: Should be `http://localhost:3000/api/auth/github/callback` for local development, or `https://your-production-url.com/api/auth/github/callback` for production.
    - **GITHUB_WEBHOOK_SECRET**: Generate and save this value:
      ```bash
      openssl rand -hex 32
      ```
      Add this value to `GITHUB_WEBHOOK_SECRET` in `apps/open-swe/.env` and `apps/web/.env`.

    Below are screenshots showing where in the GitHub App settings you can find the values for the environment variables.

    <Accordion title="GitHub App OAuth Settings">
      The following screenshots show where to find the following values:

      - `GITHUB_APP_ID`
      - `NEXT_PUBLIC_GITHUB_APP_CLIENT_ID`
      - `GITHUB_APP_CLIENT_SECRET`
      - `GITHUB_APP_REDIRECT_URI`
      - `GITHUB_APP_PRIVATE_KEY`

      ![GitHub Secrets Screenshot 1](/images/gh_app_token_screenshot_1.png)
      ![GitHub Secrets Screenshot 2](/images/gh_app_token_screenshot_2.png)
      ![GitHub Secrets Screenshot 3](/images/gh_app_token_screenshot_3.png)
    </Accordion>

    <Tip>
    Keep your GitHub App credentials secure and never commit them to version control. The `.env` files are already included in `.gitignore`.
    </Tip>

  </Step>

  <Step title="Start Development Servers">
    With all environment variables configured, start both development servers:

    **Terminal 1 - Start the LangGraph Agent:**
    ```bash
    # apps/open-swe
    yarn dev
    ```
    This starts the LangGraph server at `http://localhost:2024`

    **Terminal 2 - Start the Web Application:**
    ```bash
    # apps/web
    yarn dev
    ```
    This starts the Next.js web app at `http://localhost:3000`

    <Note>
    Both servers need to be running simultaneously for full functionality. The web app communicates with the LangGraph agent through API calls.
    </Note>

  </Step>
</Steps>

## Verification

Once both servers are running:

1. **Visit the web app**: Navigate to `http://localhost:3000`
2. **Test GitHub authentication**: Try logging in with your GitHub account

<Tip>
  If you encounter issues, check the console logs in both terminal windows for
  error messages. Common issues include missing environment variables or
  incorrect GitHub App configuration.
</Tip>

## Next Steps

- Learn about [Authentication](/labs/swe/setup/authentication) to understand how the GitHub App integration works
- Explore [Usage](/labs/swe/usage/intro) to start using Open SWE for code changes
- Review the [Monorepo Structure](/labs/swe/setup/monorepo) for development best practices
