---
title: "Introduction"
description: "An introduction to Open SWE"
---

Open SWE is an open-source cloud-based coding agent built with [LangGraph](https://langchain-ai.github.io/langgraphjs/). It's designed to autonomously understand, plan, and execute code changes across entire repositories.

## How It Works

Open SWE operates through three specialized LangGraph agents:

- **Manager Graph**: Orchestrates user interactions and coordinates between other graphs
- **Planner Graph**: Analyzes requirements and creates detailed execution plans
- **Programmer Graph**: Executes code changes based on approved plans

The agent can be used through a web interface or triggered automatically via GitHub webhooks, making it flexible for both interactive development and automated workflows.

![Open SWE UI Screenshot](/images/ui-screenshot.png)

<CardGroup cols={2}>
  <Card title="Setup" icon="robot" href="/labs/swe/setup/intro">
    How to set up Open SWE for development
  </Card>
  <Card title="Examples" icon="database" href="/labs/swe/examples">
    Examples of tasks you can try out
  </Card>
  <Card title="Usage" icon="database" href="/labs/swe/usage/intro">
    Open SWE features, and how to use them
  </Card>
</CardGroup>

## What's in These Docs

This documentation covers everything you need to know about Open SWE:

- **Usage**: How to interact with Open SWE through the web interface and GitHub webhooks
- **Setup**: Complete development environment setup including monorepo structure, dependencies, and authentication

<Tip>
  Try out the [live demo](https://swe.langchain.com) to see Open SWE in action.
</Tip>
