# Open SWE Web

The Next.js web interface for Open SWE, providing a user-friendly chat interface to interact with the LangGraph agent.

## Documentation

For detailed usage information, see the [UI documentation](https://docs.langchain.com/labs/swe/usage/ui).

## Development

1. Copy the environment file: `cp .env.example .env` and fill in the required values
2. Install dependencies: `yarn install`
3. Start the development server: `yarn dev`
