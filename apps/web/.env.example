# ------------------Github App Secrets-----------------
# GitHub app client secrets. Used for the GitHub OAuth login flow.
NEXT_PUBLIC_GITHUB_APP_CLIENT_ID=""
GITHUB_APP_CLIENT_SECRET=""
# Should be updated to your production URL when deployed.
# This value should match the redirect URL you have configured in
# your GitHub app settings.
GITHUB_APP_REDIRECT_URI="http://localhost:3000/api/auth/github/callback"

GITHUB_APP_NAME="open-swe-dev" # this must match the name of your GitHub app, excluding spaces
GITHUB_APP_ID=""
# App secret key. Should be multi-line.
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
...add your private key here...
-----END RSA PRIVATE KEY-----
"


# ------------------------Other------------------------
# The API URL of the proxy route in the Next.js app. This route forwards
# requests to the LangGraph server, injecting some secrets.
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
# The API URL of the LangGraph server. Used in the proxy route to forward
# requests to the LangGraph server.
LANGGRAPH_API_URL="http://localhost:2024"
# Encryption key for secrets (32-byte hex string for AES-256)
# Should be the same value as the one used in the web app, so that secrets
# encrypted in the web app can be decrypted in the agent.
SECRETS_ENCRYPTION_KEY=""
