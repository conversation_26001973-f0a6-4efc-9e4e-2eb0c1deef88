{"name": "@open-swe/cli", "version": "0.0.0", "license": "MIT", "private": true, "type": "module", "main": "index.js", "scripts": {"clean": "rm -rf ./dist .turbo || true", "build": "yarn clean && tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "echo \"No tests yet\" && exit 0", "dev": "tsx src/index.tsx", "cli": "npx tsc && node dist/index.js"}, "dependencies": {"@langchain/langgraph-sdk": "^0.0.95", "@open-swe/shared": "*", "commander": "^12.0.0", "dotenv": "^16.6.1", "ink": "^6.0.1", "react": "^19.1.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.19.0", "@tsconfig/recommended": "^1.0.8", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.19.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "prettier": "^3.5.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "packageManager": "yarn@3.5.1"}